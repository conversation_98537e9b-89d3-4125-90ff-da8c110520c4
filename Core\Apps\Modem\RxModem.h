/**
 * @file    RxModem.h
 * @brief   
 * <AUTHOR>
 * @version 1.0
 * @date    2025-03-23
 * 
 * @copyright Copyright (c) 2025
 */

#ifndef  __RXMODEM_H__
#define  __RXMODEM_H__

#include <memory>
#include "DataType.h"
#include "AllConst.h"
#include "AisMsg.h"
#include "GmskLib.h"

//=============================================================================
#define  DC_MIN_LEVEL_A                 (( 100.0f / 3300))  // 0.100 Volt
#define  DC_MID_LEVEL_A                 ((1600.0f / 3300))  // 1.600 Volt
#define  DC_MAX_LEVEL_A                 ((2200.0f / 3300))  // 2.200 Volt
//-----------------------------------------------------------------------------
#define  DC_MIN_LEVEL_B                 (( 100.0f / 3300))  // 0.100 Volt
#define  DC_MID_LEVEL_B                 ((1600.0f / 3300))  // 1.600 Volt
#define  DC_MAX_LEVEL_B                 ((2200.0f / 3300))  // 2.200 Volt
//=============================================================================

//=============================================================================
#define  RX_MODEM_STATUS_PREAMBLE       (0)
#define  RX_MODEM_STATUS_START          (1)
#define  RX_MODEM_STATUS_PRELOAD        (2)
#define  RX_MODEM_STATUS_DATA           (3)
//===========================================================================
#define  RX_TRAINING_BIT                (16)        // 24 -> 16
#define  RX_START_FLAG_BIT              (8)
#define  RX_OVERSAMPLE_RATE             (5)
#define  RX_RAW_DATA_BUFF_SIZE          ((RX_TRAINING_BIT+RX_START_FLAG_BIT)*RX_OVERSAMPLE_RATE)*2
//===========================================================================
#define  RX_RAW_FORM_BUFF_SIZE          (10)
//===========================================================================
#define  RX_PREAMBLE_LEN                ((RX_TRAINING_BIT+RX_START_FLAG_BIT)*RX_OVERSAMPLE_RATE)   // Training sequence(01..01) + Start flag(0x7E)
#define  RX_TRAINING_BIT_LEN            (RX_TRAINING_BIT * RX_OVERSAMPLE_RATE)      // 16 bits * 5 = 80 samples
#define  RX_START_FLAG_BIT_LEN          (RX_START_FLAG_BIT * RX_OVERSAMPLE_RATE)    // 8 bits * 5 = 40 samples
#define  RX_SYNC_THRESHOLD              (0.6084)    // 0.78*0.78 Preamble max corrolation threshold
#define  RX_SYNC_STABLE_CNT             (10)        // Stable max corrolation count
#define  RX_DATA_OFFSET_CNT             (-7)        // Data offset count       
#define  RX_MAX_ADC_ERROR_CNT           (20)        // Max adc value error count
//============================================================================
#define  RX_DOT_MAX_CNT_SIZE            (7)
#define  RX_DOT_MAX_CNT_MASK            (0x007f)
#define  RX_DOT_START_P_MASK            (0x0005)
#define  RX_DOT_DETCT_P_MASK            (0x0055)
#define  RX_DOT_MAX_CNT_LAST            (RX_DOT_MAX_CNT_SIZE - 1)
//============================================================================
#define VITERBI_NUM_STATES              (4)
#define VITERBI_MAX_SEQ_LEN             (16)
#define INF_VALUE                       (100000)

//============================================================================
// Viterbi States
typedef struct {
    double  path_metrics[VITERBI_NUM_STATES];                       // Path metrics for each state
    uint8_t sequences[VITERBI_NUM_STATES][VITERBI_MAX_SEQ_LEN];     // Decoded bit sequences for each state
    int     seq_len[VITERBI_NUM_STATES];                            // Current sequence length for each state
} ViterbiStates;

class cRxModem
{
public:
    cRxModem(int nRxChannelNo, float fRxReferMinVal, float fRxReferMidVal, float fRxReferMaxVal);
    virtual ~cRxModem(void);

    static std::shared_ptr<cRxModem> getInstRxA() {
        static std::shared_ptr<cRxModem> pInstRxA = std::make_shared<cRxModem>( AIS_CHANNEL_AIS1, 
                                                                                DC_MIN_LEVEL_A, 
                                                                                DC_MID_LEVEL_A, 
                                                                                DC_MAX_LEVEL_A
                                                                                );
        return pInstRxA;
    }

    static std::shared_ptr<cRxModem> getInstRxB() {
        static std::shared_ptr<cRxModem> pInstRxB = std::make_shared<cRxModem>( AIS_CHANNEL_AIS2, 
                                                                                DC_MIN_LEVEL_B, 
                                                                                DC_MID_LEVEL_B, 
                                                                                DC_MAX_LEVEL_B
                                                                                );
        return pInstRxB;
    }

protected:
    void  ClearPreambleBuff(void);
    void  ClearRxRawBuff(void);
    void  ClearRxRawFormTemp(void);
    void  ResetToRxStatusPreamble(void);

    float RunGmskFilter(float fInAdc);
    bool  RunDetectPreamble(float fInAdc);
    void  EstimateSignalGain(const float* fRcvPreamble,
                             const float* fSrcPreamble, 
                             int num, 
                             float fMaxImpulseResponse,
                             float* fH0,
                             float* fBias);
    void  CopySeqAndAddBit(const uint8_t* src_sequence, int src_length,
                            int* dst_sequence, int* dst_length, int bit);
    int8_t ViterbiMlsd(ViterbiStates* states, float rx_data, 
                        float main_signal_coeff, float isi_signal_coeff);
    void  ApplyGmskFilter(HWORD wRxAfAdcData);
    void  BufferRawData(void);
    void  NormalizeDcLevel(void);
    void  EstimateChannelParameters(void);
    void  InitializeViterbiDecoder(void);
    void  WritePacketIntoRxRawBuff(void);
    void  PutDataIntoRxRawBuff(UCHAR bRxData);
    int   ProcessRxDataCommonRun(void);
    void  DecodeCurrentSample(void);
    void  ProcessPreambleDetection(void);
    void  ProcessStartSequence(void);
    void  ProcessDataDecoding(void);

public:
    int   ProcessGmskRxData(HWORD wRxAfAdcData);
    xAisRxRawForm* GetFullPacketFromRxRawBuff(void);

protected:
    volatile int    m_nRxChannelNo;
    volatile float  m_fRxReferMinVal;
    volatile float  m_fRxReferMidVal;
    volatile float  m_fRxReferMaxVal;

    volatile float  m_pRxRawDataBuff[RX_RAW_DATA_BUFF_SIZE];
    volatile int    m_dwRxRawDataIdx;

    volatile float  m_pRxPreambleBuff[RX_PREAMBLE_LEN];
    volatile float  m_pRxRawGmskBuff[RX_GMSK_BT_0_5_FIR_N];
    volatile int    m_nGmskBuffIdx;  // GMSK 필터 순환 버퍼 인덱스

    volatile HWORD  m_wBitSamplCntr;
    volatile float  m_fRxReferValue;

    volatile HWORD  m_wRxRunStatus;
    volatile HWORD  m_wRxBitCount;
    volatile HWORD  m_wRxShiftReg;

    volatile HWORD  m_wReceivingID;
    volatile HWORD  m_wRxMaxBitSize;

    volatile int8_t m_nRxPrevBitD;
    volatile int8_t m_nRxCurrBitD;

    volatile HWORD  m_wNewBitData;
    volatile HWORD  m_wCrcRegData;
    volatile UCHAR  m_bRxByteData;

    volatile xAisRxRawForm *m_vRxRawFormBuff;
    volatile int    m_nRxRawFormHead;
    volatile int    m_nRxRawFormTail;
    volatile int    m_nRxRawFormTemp;

    volatile float  m_fRxAfAdcData;

    volatile DWORD  m_dRxAdcErrCnt;

    volatile float  m_fRxMovingSum;
    volatile float  m_vRxDcLevelBuff[RX_TRAINING_BIT_LEN];
    volatile HWORD  m_nRxDcLevelIdx;

    volatile float  m_fRxNormAdc;
    volatile float  m_vRxNormBuff[RX_PREAMBLE_LEN];
    volatile int    m_dRxNormBuffIdx;
    volatile float  m_fMaxCorrVal;
    volatile int    m_dwMaxCorrIdx;
    volatile DWORD  m_dwMaxElapseCount;
    volatile int    m_dwRxDataStartIdx;
    volatile UCHAR  m_nRxOvsCnt;
    volatile float  m_fRxSyncPowSum;

    volatile float  m_fFilteredSyncSum;
    volatile float  m_fFilteredSyncPowSum;

    volatile float  m_fSignalGain;
    volatile float  m_fSignalCoff;

    volatile DWORD  m_dSampleCounter;
    volatile DWORD  m_dSlotNoCounter;

    volatile ViterbiStates m_vViterbiStates;
    volatile bool m_bFirstBitSkipped;
};

#endif /*__RXMODEM_H__*/

